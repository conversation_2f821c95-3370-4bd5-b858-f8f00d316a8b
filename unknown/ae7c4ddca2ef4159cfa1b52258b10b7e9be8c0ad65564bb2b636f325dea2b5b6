<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.profile.ProfileFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="280dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:contentScrim="@color/primary"
            app:expandedTitleMarginEnd="64dp"
            app:expandedTitleMarginStart="48dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/profile_header_background"
                app:layout_collapseMode="parallax">

                <!-- Modern circular avatar with shadow -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/profile_image_container"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    app:cardCornerRadius="60dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="@color/white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.35">

                    <ImageView
                        android:id="@+id/profile_image"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:contentDescription="@string/profile__image"
                        android:src="@drawable/ic_person"
                        android:padding="24dp"
                        android:scaleType="centerInside"
                        android:tint="@color/primary" />

                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:id="@+id/profile_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/profile_image_container"
                    tools:text="John Doe" />

                <TextView
                    android:id="@+id/profile_position"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    android:textColor="@color/white"
                    android:alpha="0.9"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/profile_name"
                    tools:text="Developer at ACME Inc" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Change Password Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_change_password"
                style="@style/Widget.Material3.Button.TonalButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/profile__change_password"
                android:layout_marginBottom="24dp"
                android:textColor="@color/change_password_button_fg"
                android:backgroundTint="@color/change_password_button_bg"
                app:cornerRadius="8dp" />
                <!-- app:icon="@drawable/ic_lock"
                app:iconGravity="textStart"
                app:iconPadding="8dp" -->

            <!-- Personal Information Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_personal_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/surface">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="اطلاعات شخصی"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <!-- First Name -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__first_name"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/profile_first_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            tools:text="John" />
                    </LinearLayout>

                    <!-- Last Name -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__last_name"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/profile_last_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            tools:text="Doe" />
                    </LinearLayout>

                    <!-- Gender -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__gender"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/profile_gender"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            tools:text="Male" />
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Contact Information Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_contact_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/surface">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="اطلاعات تماس"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <!-- Username -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__username"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/profile_username"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            tools:text="user1" />
                    </LinearLayout>

                    <!-- Email -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__email"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/profile_email"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            tools:text="<EMAIL>" />
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Company Information Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_company_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/surface">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="اطلاعات شغلی"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <!-- Company -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__company"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/profile_company"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            tools:text="ACME Inc" />
                    </LinearLayout>

                    <!-- Position -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__position"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/profile_position_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            tools:text="Developer" />
                    </LinearLayout>

                    <!-- Description -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__description"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/profile_description"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            tools:text="Software developer with 5 years of experience" />
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- System Information Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/surface">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="اطلاعات سیستم"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <!-- Is Superuser -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__is_superuser"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/profile_is_superuser"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            tools:text="No" />
                    </LinearLayout>

                    <!-- Is Limited Admin -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__is_limited_admin"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/profile_is_limited_admin"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            tools:text="Yes" />
                    </LinearLayout>

                    <!-- Short UUID -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile__short_uuid"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/profile_short_uuid"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            android:fontFamily="monospace"
                            tools:text="da1e120e" />
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Loading and Error States -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="32dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/error_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="32dp"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                android:textColor="@color/error"
                android:visibility="gone"
                tools:text="Error loading profile" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
