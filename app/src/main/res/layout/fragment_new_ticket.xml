<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="6dp"
    tools:context=".ui.newticket.NewTicketFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- New Ticket Form Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_new_ticket"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tv_new_ticket_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/new_ticket"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textColor="?attr/colorOnSurface"
                        android:textStyle="bold" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_ticket_title"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/ticket_title">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_ticket_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <LinearLayout
                        android:id="@+id/layout_dropdowns"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_category"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="@string/floating_label__category"
                            app:hintTextAppearance="@style/TextAppearance.MaterialComponents.Caption">

                            <AutoCompleteTextView
                                android:id="@+id/dropdown_category"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:textSize="12sp"
                                android:paddingTop="8dp"
                                android:paddingBottom="8dp" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_priority"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:hint="@string/floating_label__priority"
                            app:hintTextAppearance="@style/TextAppearance.MaterialComponents.Caption">

                            <AutoCompleteTextView
                                android:id="@+id/dropdown_priority"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:textSize="12sp"
                                android:paddingTop="8dp"
                                android:paddingBottom="8dp" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_message"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/ticket_message">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_message"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="top|start"
                            android:inputType="textMultiLine"
                            android:lines="5"
                            android:minLines="5" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <LinearLayout
                        android:id="@+id/layout_file"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <Button
                                android:id="@+id/btn_select_file"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginEnd="8dp"
                                android:text="@string/select_file"
                                android:textColor="@color/select_file_button_fg"
                                android:backgroundTint="@color/select_file_button_bg"
                                app:strokeWidth="0dp"
                                app:icon="@drawable/ic_file_upload" />

                            <Button
                                android:id="@+id/btn_take_photo"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginStart="8dp"
                                android:text="@string/take_photo"
                                android:textColor="@color/take_photo_button_fg"
                                android:backgroundTint="@color/take_photo_button_bg"
                                app:strokeWidth="0dp"
                                app:icon="@drawable/ic_camera_alt" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_selected_file"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:ellipsize="middle"
                            android:singleLine="true"
                            android:visibility="gone"
                            tools:text="selected_file.pdf"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <Button
                        android:id="@+id/btn_submit"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/submit"
                        android:textColor="@color/submit_button_fg"
                        android:backgroundTint="@color/submit_button_bg"
                        app:strokeWidth="0dp" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="4dp"
        android:gravity="center"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="?attr/colorError"
        android:visibility="gone"
        tools:text="Error message"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
